import { Context, Next } from 'hono'

type Bindings = {
  DB: D1Database
  NODE_ENV: string
}

// Middleware xác thực c<PERSON> bản (c<PERSON> thể mở rộng sau)
export const authMiddleware = async (c: Context<{ Bindings: Bindings }>, next: Next) => {
  // Tạm thời bỏ qua xác thực cho development
  if (c.env.NODE_ENV === 'development') {
    await next()
    return
  }

  // TODO: Implement JWT authentication
  const authHeader = c.req.header('Authorization')
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return c.json({
      success: false,
      error: 'Unauthorized - Missing or invalid token'
    }, 401)
  }

  // TODO: Verify JWT token
  // const token = authHeader.substring(7)
  // const isValid = await verifyJWT(token)
  
  // if (!isValid) {
  //   return c.json({
  //     success: false,
  //     error: 'Unauthorized - Invalid token'
  //   }, 401)
  // }

  await next()
}

// Middleware ghi log hoạt động
export const activityLogger = async (c: Context<{ Bindings: Bindings }>, next: Next) => {
  const startTime = Date.now()
  
  await next()
  
  const endTime = Date.now()
  const duration = endTime - startTime
  
  // Log request info
  console.log(`${c.req.method} ${c.req.url} - ${c.res.status} - ${duration}ms`)
  
  // TODO: Save to activity_history table if needed
  // if (c.req.method !== 'GET') {
  //   await logActivity(c)
  // }
}

// Middleware validation cho request body
export const validateRequest = (schema: any) => {
  return async (c: Context, next: Next) => {
    try {
      if (c.req.method === 'POST' || c.req.method === 'PUT') {
        const body = await c.req.json()
        // TODO: Implement validation with Zod
        // const validatedData = schema.parse(body)
        // c.set('validatedData', validatedData)
      }
      await next()
    } catch (error) {
      return c.json({
        success: false,
        error: 'Invalid request data',
        details: error instanceof Error ? error.message : 'Unknown validation error'
      }, 400)
    }
  }
}
