import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { WalkieTalkieManager } from "@/components/WalkieTalkieManager";
import { AccessCardManager } from "@/components/AccessCardManager";
import { ActivityHistory } from "@/components/ActivityHistory";
import { BarChart3, Radio, CreditCard, Users, Clock, Loader2 } from "lucide-react";
import { useWalkieTalkies, useAccessCards } from "@/hooks/api";

const Index = () => {
  // Fetch data từ API
  const { data: walkieTalkies = [], isLoading: isLoadingWalkieTalkies, error: walkieTalkiesError } = useWalkieTalkies();
  const { data: accessCards = [], isLoading: isLoadingAccessCards, error: accessCardsError } = useAccessCards();

  const isLoading = isLoadingWalkieTalkies || isLoadingAccessCards;
  const hasError = walkieTalkiesError || accessCardsError;

  // Calculate statistics
  const totalItems = walkieTalkies.length + accessCards.length;
  const assignedWalkieTalkies = walkieTalkies.filter(item => item.status === "assigned").length;
  const assignedAccessCards = accessCards.filter(item => item.assigned_to).length;
  const assignedItems = assignedWalkieTalkies + assignedAccessCards;
  const availableItems = totalItems - assignedItems;

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Đang tải dữ liệu...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (hasError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
            <h2 className="text-red-800 font-semibold mb-2">Lỗi tải dữ liệu</h2>
            <p className="text-red-600 text-sm">
              {walkieTalkiesError?.message || accessCardsError?.message || 'Không thể kết nối đến server'}
            </p>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Thử lại
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-3">
              <div className="bg-blue-600 p-2 rounded-lg">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Quản lý Thiết bị</h1>
                <p className="text-sm text-gray-500">Hệ thống quản lý bộ đàm và thẻ ra vào</p>
              </div>
            </div>
            <div className="text-sm text-gray-500">
              {new Date().toLocaleDateString('vi-VN', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-white hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Tổng thiết bị</CardTitle>
              <div className="bg-blue-100 p-2 rounded-full">
                <BarChart3 className="h-4 w-4 text-blue-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{totalItems}</div>
              <p className="text-xs text-gray-500">Bộ đàm và thẻ ra vào</p>
            </CardContent>
          </Card>

          <Card className="bg-white hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Đang mượn</CardTitle>
              <div className="bg-orange-100 p-2 rounded-full">
                <Clock className="h-4 w-4 text-orange-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{assignedItems}</div>
              <p className="text-xs text-gray-500">Thiết bị đang được sử dụng</p>
            </CardContent>
          </Card>

          <Card className="bg-white hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Có sẵn</CardTitle>
              <div className="bg-green-100 p-2 rounded-full">
                <Users className="h-4 w-4 text-green-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{availableItems}</div>
              <p className="text-xs text-gray-500">Sẵn sàng cho mượn</p>
            </CardContent>
          </Card>

          <Card className="bg-white hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Tỷ lệ sử dụng</CardTitle>
              <div className="bg-purple-100 p-2 rounded-full">
                <BarChart3 className="h-4 w-4 text-purple-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {totalItems > 0 ? Math.round((assignedItems / totalItems) * 100) : 0}%
              </div>
              <p className="text-xs text-gray-500">Thiết bị đang sử dụng</p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="walkie-talkies" className="space-y-6">
          <TabsList className="bg-white p-1 rounded-lg shadow-sm">
            <TabsTrigger value="walkie-talkies" className="flex items-center space-x-2">
              <Radio className="h-4 w-4" />
              <span>Bộ đàm</span>
            </TabsTrigger>
            <TabsTrigger value="access-cards" className="flex items-center space-x-2">
              <CreditCard className="h-4 w-4" />
              <span>Thẻ ra vào</span>
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center space-x-2">
              <Clock className="h-4 w-4" />
              <span>Lịch sử</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="walkie-talkies">
            <WalkieTalkieManager initialData={walkieTalkies} />
          </TabsContent>

          <TabsContent value="access-cards">
            <AccessCardManager initialData={accessCards} />
          </TabsContent>

          <TabsContent value="history">
            <ActivityHistory />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Index;
