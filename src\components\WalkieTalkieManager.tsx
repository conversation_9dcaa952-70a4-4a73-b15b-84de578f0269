
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Radio, Plus, UserCheck, UserX, Search, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useForm } from "@tanstack/react-form";
import { addDeviceSchema, borrowDeviceSchema, type AddDeviceFormData, type BorrowDeviceFormData } from "@/lib/schemas";
import { useCreateWalkieTalkie, useUpdateWalkieTalkie, useDeleteWalkieTalkie, useCreateActivityRecord } from "@/hooks/api";
import type { WalkieTalkie } from "@/lib/api";

interface WalkieTalkieManagerProps {
  initialData: WalkieTalkie[];
}

export const WalkieTalkieManager = ({ initialData }: WalkieTalkieManagerProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isBorrowDialogOpen, setIsBorrowDialogOpen] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<WalkieTalkie | null>(null);
  const { toast } = useToast();

  // API hooks
  const createWalkieTalkie = useCreateWalkieTalkie();
  const updateWalkieTalkie = useUpdateWalkieTalkie();
  const deleteWalkieTalkie = useDeleteWalkieTalkie();
  const createActivityRecord = useCreateActivityRecord();

  // TanStack Form cho thêm thiết bị
  const addDeviceForm = useForm({
    defaultValues: {
      name: '',
    } as AddDeviceFormData,
    validators: {
      onChange: ({ value }) => {
        const result = addDeviceSchema.safeParse(value);
        if (!result.success) {
          // Convert Zod v4 errors to field errors format
          const fieldErrors: Record<string, string[]> = {};
          result.error.issues.forEach(issue => {
            const path = issue.path.join('.');
            if (!fieldErrors[path]) {
              fieldErrors[path] = [];
            }
            fieldErrors[path].push(issue.message);
          });
          return fieldErrors;
        }
        return undefined;
      },
    },
    onSubmit: async ({ value }) => {
      try {
        await createWalkieTalkie.mutateAsync({
          model: value.name,
          serial_number: `WT${Date.now()}`, // Generate serial number
          status: 'available',
          location: 'Kho thiết bị',
        });

        setIsAddDialogOpen(false);
        addDeviceForm.reset();
      } catch (error) {
        // Error handling is done in the hook
      }
    },
  });

  // TanStack Form cho cho mượn thiết bị
  const borrowDeviceForm = useForm({
    defaultValues: {
      borrowerName: '',
    } as BorrowDeviceFormData,
    validators: {
      onChange: ({ value }) => {
        const result = borrowDeviceSchema.safeParse(value);
        if (!result.success) {
          // Convert Zod v4 errors to field errors format
          const fieldErrors: Record<string, string[]> = {};
          result.error.issues.forEach(issue => {
            const path = issue.path.join('.');
            if (!fieldErrors[path]) {
              fieldErrors[path] = [];
            }
            fieldErrors[path].push(issue.message);
          });
          return fieldErrors;
        }
        return undefined;
      },
    },
    onSubmit: async ({ value }) => {
      if (!selectedDevice) return;

      try {
        await updateWalkieTalkie.mutateAsync({
          id: selectedDevice.id,
          data: {
            status: 'assigned',
            assigned_to: value.borrowerName,
          }
        });

        // Create activity record
        await createActivityRecord.mutateAsync({
          item_type: 'walkie_talkie',
          item_id: selectedDevice.id,
          action: 'assign',
          description: `Giao bộ đàm ${selectedDevice.model} cho ${value.borrowerName}`,
          user_id: 'admin', // TODO: Get from auth context
        });

        setIsBorrowDialogOpen(false);
        setSelectedDevice(null);
        borrowDeviceForm.reset();
      } catch (error) {
        // Error handling is done in the hook
      }
    },
  });

  const filteredDevices = initialData.filter(device =>
    device.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
    device.serial_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (device.assigned_to && device.assigned_to.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const returnDevice = async (device: WalkieTalkie) => {
    try {
      await updateWalkieTalkie.mutateAsync({
        id: device.id,
        data: {
          status: 'available',
          assigned_to: null,
        }
      });

      // Create activity record
      await createActivityRecord.mutateAsync({
        item_type: 'walkie_talkie',
        item_id: device.id,
        action: 'return',
        description: `Thu hồi bộ đàm ${device.model} từ ${device.assigned_to}`,
        user_id: 'admin', // TODO: Get from auth context
      });
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const openBorrowDialog = (device: WalkieTalkie) => {
    setSelectedDevice(device);
    borrowDeviceForm.reset(); // Reset form khi mở dialog
    setIsBorrowDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      <Card className="bg-white shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Radio className="h-5 w-5 text-blue-600" />
              <span>Quản lý Bộ đàm</span>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm bộ đàm
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Thêm bộ đàm mới</DialogTitle>
                </DialogHeader>
                <form
                  onSubmit={(e) => {
                    e.preventDefault()
                    addDeviceForm.handleSubmit()
                  }}
                  className="space-y-4"
                >
                  <addDeviceForm.Field
                    name="name"
                    children={(field) => (
                      <div>
                        <Label htmlFor="device-name">Tên bộ đàm</Label>
                        <Input
                          id="device-name"
                          value={field.state.value}
                          onChange={(e) => field.handleChange(e.target.value)}
                          onBlur={field.handleBlur}
                          placeholder="VD: Bộ đàm 004"
                        />
                        {field.state.meta.errors.length > 0 && (
                          <p className="text-sm text-red-500 mt-1">
                            {field.state.meta.errors[0] as string}
                          </p>
                        )}
                      </div>
                    )}
                  />
                  <addDeviceForm.Subscribe
                    selector={(state) => [state.canSubmit, state.isSubmitting]}
                    children={([canSubmit, isSubmitting]) => (
                      <Button type="submit" disabled={!canSubmit || isSubmitting} className="w-full">
                        {isSubmitting ? 'Đang thêm...' : 'Thêm'}
                      </Button>
                    )}
                  />
                </form>
              </DialogContent>
            </Dialog>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Tìm kiếm bộ đàm hoặc người mượn..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredDevices.map((device) => (
              <Card key={device.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h3 className="font-semibold text-gray-900">{device.model}</h3>
                      <p className="text-sm text-gray-500">{device.serial_number}</p>
                    </div>
                    <Badge
                      variant={device.status === "available" ? "default" : "destructive"}
                      className={device.status === "available" ? "bg-green-100 text-green-800" : "bg-orange-100 text-orange-800"}
                    >
                      {device.status === "available" ? "Có sẵn" :
                       device.status === "assigned" ? "Đã giao" :
                       device.status === "maintenance" ? "Bảo trì" : "Mất"}
                    </Badge>
                  </div>
                  
                  {device.status === "assigned" && device.assigned_to && (
                    <div className="mb-3 text-sm text-gray-600">
                      <p><strong>Người được giao:</strong> {device.assigned_to}</p>
                      {device.location && <p><strong>Vị trí:</strong> {device.location}</p>}
                      {device.frequency && <p><strong>Tần số:</strong> {device.frequency}</p>}
                    </div>
                  )}
                  
                  <div className="flex space-x-2">
                    {device.status === "available" ? (
                      <Button
                        onClick={() => openBorrowDialog(device)}
                        className="flex-1 bg-blue-600 hover:bg-blue-700"
                        size="sm"
                      >
                        <UserCheck className="h-4 w-4 mr-1" />
                        Giao thiết bị
                      </Button>
                    ) : device.status === "assigned" ? (
                      <Button
                        onClick={() => returnDevice(device)}
                        variant="outline"
                        className="flex-1"
                        size="sm"
                      >
                        <UserX className="h-4 w-4 mr-1" />
                        Thu hồi
                      </Button>
                    ) : (
                      <Button
                        variant="outline"
                        className="flex-1"
                        size="sm"
                        disabled
                      >
                        {device.status === "maintenance" ? "Đang bảo trì" : "Không khả dụng"}
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Borrow Dialog */}
      <Dialog open={isBorrowDialogOpen} onOpenChange={setIsBorrowDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Giao thiết bị {selectedDevice?.model}</DialogTitle>
          </DialogHeader>
          <form
            onSubmit={(e) => {
              e.preventDefault()
              borrowDeviceForm.handleSubmit()
            }}
            className="space-y-4"
          >
            <borrowDeviceForm.Field
              name="borrowerName"
              children={(field) => (
                <div>
                  <Label htmlFor="borrower-name">Tên người được giao</Label>
                  <Input
                    id="borrower-name"
                    value={field.state.value}
                    onChange={(e) => field.handleChange(e.target.value)}
                    onBlur={field.handleBlur}
                    placeholder="Nhập tên người được giao thiết bị"
                  />
                  {field.state.meta.errors.length > 0 && (
                    <p className="text-sm text-red-500 mt-1">
                      {field.state.meta.errors[0] as string}
                    </p>
                  )}
                </div>
              )}
            />
            <borrowDeviceForm.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
              children={([canSubmit, isSubmitting]) => (
                <Button type="submit" disabled={!canSubmit || isSubmitting} className="w-full">
                  {isSubmitting ? 'Đang xử lý...' : 'Xác nhận giao thiết bị'}
                </Button>
              )}
            />
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};
