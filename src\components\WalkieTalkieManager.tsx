
import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Radio, Plus, UserCheck, UserX, Search } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useForm } from "@tanstack/react-form";
import { addDeviceSchema, borrowDeviceSchema, type AddDeviceFormData, type BorrowDeviceFormData } from "@/lib/schemas";

interface WalkieTalkie {
  id: number;
  name: string;
  status: "available" | "borrowed";
  borrower: string | null;
  borrowedAt: Date | null;
}

interface WalkieTalkieManagerProps {
  initialData: WalkieTalkie[];
}

export const WalkieTalkieManager = ({ initialData }: WalkieTalkieManagerProps) => {
  const [walkieTalkies, setWalkieTalkies] = useState<WalkieTalkie[]>(initialData);
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isBorrowDialogOpen, setIsBorrowDialogOpen] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<WalkieTalkie | null>(null);
  const { toast } = useToast();

  // TanStack Form cho thêm thiết bị
  const addDeviceForm = useForm({
    defaultValues: {
      name: '',
    } as AddDeviceFormData,
    validators: {
      onChange: ({ value }) => {
        const result = addDeviceSchema.safeParse(value);
        if (!result.success) {
          // Convert Zod v4 errors to field errors format
          const fieldErrors: Record<string, string[]> = {};
          result.error.issues.forEach(issue => {
            const path = issue.path.join('.');
            if (!fieldErrors[path]) {
              fieldErrors[path] = [];
            }
            fieldErrors[path].push(issue.message);
          });
          return fieldErrors;
        }
        return undefined;
      },
    },
    onSubmit: ({ value }) => {
      const newDevice: WalkieTalkie = {
        id: Date.now(),
        name: value.name,
        status: "available",
        borrower: null,
        borrowedAt: null
      };

      setWalkieTalkies([...walkieTalkies, newDevice]);
      setIsAddDialogOpen(false);
      addDeviceForm.reset();

      toast({
        title: "Thành công",
        description: `Đã thêm bộ đàm ${value.name}`,
      });
    },
  });

  // TanStack Form cho cho mượn thiết bị
  const borrowDeviceForm = useForm({
    defaultValues: {
      borrowerName: '',
    } as BorrowDeviceFormData,
    validators: {
      onChange: ({ value }) => {
        const result = borrowDeviceSchema.safeParse(value);
        if (!result.success) {
          // Convert Zod v4 errors to field errors format
          const fieldErrors: Record<string, string[]> = {};
          result.error.issues.forEach(issue => {
            const path = issue.path.join('.');
            if (!fieldErrors[path]) {
              fieldErrors[path] = [];
            }
            fieldErrors[path].push(issue.message);
          });
          return fieldErrors;
        }
        return undefined;
      },
    },
    onSubmit: ({ value }) => {
      if (!selectedDevice) return;

      const updatedDevices = walkieTalkies.map(device =>
        device.id === selectedDevice.id
          ? {
              ...device,
              status: "borrowed" as const,
              borrower: value.borrowerName,
              borrowedAt: new Date()
            }
          : device
      );

      setWalkieTalkies(updatedDevices);
      setIsBorrowDialogOpen(false);
      setSelectedDevice(null);
      borrowDeviceForm.reset();

      toast({
        title: "Cho mượn thành công",
        description: `${selectedDevice.name} đã được cho ${value.borrowerName} mượn`,
      });
    },
  });

  const filteredDevices = walkieTalkies.filter(device =>
    device.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (device.borrower && device.borrower.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const returnDevice = (device: WalkieTalkie) => {
    const updatedDevices = walkieTalkies.map(d =>
      d.id === device.id
        ? {
            ...d,
            status: "available" as const,
            borrower: null,
            borrowedAt: null
          }
        : d
    );
    
    setWalkieTalkies(updatedDevices);
    
    toast({
      title: "Trả thiết bị thành công",
      description: `${device.name} đã được trả về`,
    });
  };

  const openBorrowDialog = (device: WalkieTalkie) => {
    setSelectedDevice(device);
    borrowDeviceForm.reset(); // Reset form khi mở dialog
    setIsBorrowDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      <Card className="bg-white shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Radio className="h-5 w-5 text-blue-600" />
              <span>Quản lý Bộ đàm</span>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm bộ đàm
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Thêm bộ đàm mới</DialogTitle>
                </DialogHeader>
                <form
                  onSubmit={(e) => {
                    e.preventDefault()
                    addDeviceForm.handleSubmit()
                  }}
                  className="space-y-4"
                >
                  <addDeviceForm.Field
                    name="name"
                    children={(field) => (
                      <div>
                        <Label htmlFor="device-name">Tên bộ đàm</Label>
                        <Input
                          id="device-name"
                          value={field.state.value}
                          onChange={(e) => field.handleChange(e.target.value)}
                          onBlur={field.handleBlur}
                          placeholder="VD: Bộ đàm 004"
                        />
                        {field.state.meta.errors.length > 0 && (
                          <p className="text-sm text-red-500 mt-1">
                            {field.state.meta.errors[0] as string}
                          </p>
                        )}
                      </div>
                    )}
                  />
                  <addDeviceForm.Subscribe
                    selector={(state) => [state.canSubmit, state.isSubmitting]}
                    children={([canSubmit, isSubmitting]) => (
                      <Button type="submit" disabled={!canSubmit || isSubmitting} className="w-full">
                        {isSubmitting ? 'Đang thêm...' : 'Thêm'}
                      </Button>
                    )}
                  />
                </form>
              </DialogContent>
            </Dialog>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Tìm kiếm bộ đàm hoặc người mượn..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredDevices.map((device) => (
              <Card key={device.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold text-gray-900">{device.name}</h3>
                    <Badge 
                      variant={device.status === "available" ? "default" : "destructive"}
                      className={device.status === "available" ? "bg-green-100 text-green-800" : "bg-orange-100 text-orange-800"}
                    >
                      {device.status === "available" ? "Có sẵn" : "Đang mượn"}
                    </Badge>
                  </div>
                  
                  {device.status === "borrowed" && (
                    <div className="mb-3 text-sm text-gray-600">
                      <p><strong>Người mượn:</strong> {device.borrower}</p>
                      <p><strong>Thời gian:</strong> {device.borrowedAt?.toLocaleString('vi-VN')}</p>
                    </div>
                  )}
                  
                  <div className="flex space-x-2">
                    {device.status === "available" ? (
                      <Button 
                        onClick={() => openBorrowDialog(device)}
                        className="flex-1 bg-blue-600 hover:bg-blue-700"
                        size="sm"
                      >
                        <UserCheck className="h-4 w-4 mr-1" />
                        Cho mượn
                      </Button>
                    ) : (
                      <Button 
                        onClick={() => returnDevice(device)}
                        variant="outline"
                        className="flex-1"
                        size="sm"
                      >
                        <UserX className="h-4 w-4 mr-1" />
                        Trả về
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Borrow Dialog */}
      <Dialog open={isBorrowDialogOpen} onOpenChange={setIsBorrowDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cho mượn {selectedDevice?.name}</DialogTitle>
          </DialogHeader>
          <form
            onSubmit={(e) => {
              e.preventDefault()
              borrowDeviceForm.handleSubmit()
            }}
            className="space-y-4"
          >
            <borrowDeviceForm.Field
              name="borrowerName"
              children={(field) => (
                <div>
                  <Label htmlFor="borrower-name">Tên người mượn</Label>
                  <Input
                    id="borrower-name"
                    value={field.state.value}
                    onChange={(e) => field.handleChange(e.target.value)}
                    onBlur={field.handleBlur}
                    placeholder="Nhập tên người mượn"
                  />
                  {field.state.meta.errors.length > 0 && (
                    <p className="text-sm text-red-500 mt-1">
                      {field.state.meta.errors[0] as string}
                    </p>
                  )}
                </div>
              )}
            />
            <borrowDeviceForm.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
              children={([canSubmit, isSubmitting]) => (
                <Button type="submit" disabled={!canSubmit || isSubmitting} className="w-full">
                  {isSubmitting ? 'Đang xử lý...' : 'Xác nhận cho mượn'}
                </Button>
              )}
            />
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};
