// API client cho Quan Ly TTB
const API_BASE_URL = import.meta.env.DEV 
  ? 'http://localhost:8787' // Wrangler dev server
  : '/'; // Production (same domain)

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  total?: number;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Database types (match với schema)
export interface WalkieTalkie {
  id: number;
  model: string;
  serial_number: string;
  frequency?: string;
  status: 'available' | 'assigned' | 'maintenance' | 'lost';
  location?: string;
  assigned_to?: string;
  created_at: string;
  updated_at: string;
}

export interface AccessCard {
  id: number;
  card_number: string;
  card_type: 'employee' | 'visitor' | 'contractor' | 'admin';
  access_level: 'basic' | 'medium' | 'high' | 'admin';
  status: 'active' | 'inactive' | 'expired' | 'lost';
  assigned_to?: string;
  expiry_date?: string;
  created_at: string;
  updated_at: string;
}

export interface ActivityHistory {
  id: number;
  item_type: 'walkie_talkie' | 'access_card';
  item_id: number;
  action: 'create' | 'update' | 'delete' | 'assign' | 'return' | 'maintenance';
  description?: string;
  user_id?: string;
  old_values?: string; // JSON string
  new_values?: string; // JSON string
  created_at: string;
}

// Request types
export interface CreateWalkieTalkieRequest {
  model: string;
  serial_number: string;
  frequency?: string;
  status?: 'available' | 'assigned' | 'maintenance' | 'lost';
  location?: string;
  assigned_to?: string;
}

export interface UpdateWalkieTalkieRequest extends Partial<CreateWalkieTalkieRequest> {}

export interface CreateAccessCardRequest {
  card_number: string;
  card_type: 'employee' | 'visitor' | 'contractor' | 'admin';
  access_level?: 'basic' | 'medium' | 'high' | 'admin';
  status?: 'active' | 'inactive' | 'expired' | 'lost';
  assigned_to?: string;
  expiry_date?: string;
}

export interface UpdateAccessCardRequest extends Partial<CreateAccessCardRequest> {}

export interface CreateActivityRequest {
  item_type: 'walkie_talkie' | 'access_card';
  item_id: number;
  action: 'create' | 'update' | 'delete' | 'assign' | 'return' | 'maintenance';
  description?: string;
  user_id?: string;
  old_values?: any;
  new_values?: any;
}

// API Client class
class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Walkie Talkies API
  async getWalkieTalkies(): Promise<ApiResponse<WalkieTalkie[]>> {
    return this.request<WalkieTalkie[]>('/api/walkie-talkies');
  }

  async getWalkieTalkie(id: number): Promise<ApiResponse<WalkieTalkie>> {
    return this.request<WalkieTalkie>(`/api/walkie-talkies/${id}`);
  }

  async createWalkieTalkie(data: CreateWalkieTalkieRequest): Promise<ApiResponse<WalkieTalkie>> {
    return this.request<WalkieTalkie>('/api/walkie-talkies', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateWalkieTalkie(id: number, data: UpdateWalkieTalkieRequest): Promise<ApiResponse<void>> {
    return this.request<void>(`/api/walkie-talkies/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteWalkieTalkie(id: number): Promise<ApiResponse<void>> {
    return this.request<void>(`/api/walkie-talkies/${id}`, {
      method: 'DELETE',
    });
  }

  // Access Cards API
  async getAccessCards(): Promise<ApiResponse<AccessCard[]>> {
    return this.request<AccessCard[]>('/api/access-cards');
  }

  async getAccessCard(id: number): Promise<ApiResponse<AccessCard>> {
    return this.request<AccessCard>(`/api/access-cards/${id}`);
  }

  async createAccessCard(data: CreateAccessCardRequest): Promise<ApiResponse<AccessCard>> {
    return this.request<AccessCard>('/api/access-cards', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateAccessCard(id: number, data: UpdateAccessCardRequest): Promise<ApiResponse<void>> {
    return this.request<void>(`/api/access-cards/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteAccessCard(id: number): Promise<ApiResponse<void>> {
    return this.request<void>(`/api/access-cards/${id}`, {
      method: 'DELETE',
    });
  }

  async getAccessCardsByUser(userId: string): Promise<ApiResponse<AccessCard[]>> {
    return this.request<AccessCard[]>(`/api/access-cards/by-user/${userId}`);
  }

  // Activity History API
  async getActivityHistory(params?: {
    page?: number;
    limit?: number;
    type?: 'walkie_talkie' | 'access_card';
    action?: string;
  }): Promise<ApiResponse<ActivityHistory[]>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.type) searchParams.set('type', params.type);
    if (params?.action) searchParams.set('action', params.action);

    const query = searchParams.toString();
    return this.request<ActivityHistory[]>(`/api/history${query ? `?${query}` : ''}`);
  }

  async getActivityRecord(id: number): Promise<ApiResponse<ActivityHistory>> {
    return this.request<ActivityHistory>(`/api/history/${id}`);
  }

  async createActivityRecord(data: CreateActivityRequest): Promise<ApiResponse<ActivityHistory>> {
    return this.request<ActivityHistory>('/api/history', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getItemHistory(type: 'walkie_talkie' | 'access_card', id: number): Promise<ApiResponse<ActivityHistory[]>> {
    return this.request<ActivityHistory[]>(`/api/history/item/${type}/${id}`);
  }

  async getUserHistory(userId: string, params?: {
    page?: number;
    limit?: number;
  }): Promise<ApiResponse<ActivityHistory[]>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());

    const query = searchParams.toString();
    return this.request<ActivityHistory[]>(`/api/history/user/${userId}${query ? `?${query}` : ''}`);
  }

  // Health check
  async healthCheck(): Promise<ApiResponse<any>> {
    return this.request<any>('/');
  }
}

// Export singleton instance
export const apiClient = new ApiClient(API_BASE_URL);
export default apiClient;
