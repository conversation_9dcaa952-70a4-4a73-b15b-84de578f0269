import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { logger } from 'hono/logger'
import { prettyJSON } from 'hono/pretty-json'

// Import routes
import walkieTalkiesRoutes from './routes/walkie-talkies'
import accessCardsRoutes from './routes/access-cards'
import historyRoutes from './routes/history'

// Type definitions for Cloudflare Workers environment
type Bindings = {
  DB: D1Database
  NODE_ENV: string
}

// Create main Hono app
const app = new Hono<{ Bindings: Bindings }>()

// Global middleware
app.use('*', cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'], // Add your frontend URLs
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}))

app.use('*', logger())
app.use('*', prettyJSON())

// Health check endpoint
app.get('/', (c) => {
  return c.json({
    message: 'Quan Ly TTB API is running!',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    environment: c.env.NODE_ENV || 'development'
  })
})

// API routes
app.route('/api/walkie-talkies', walkieTalkiesRoutes)
app.route('/api/access-cards', accessCardsRoutes)
app.route('/api/history', historyRoutes)

// 404 handler
app.notFound((c) => {
  return c.json({ error: 'Not Found', message: 'The requested endpoint does not exist' }, 404)
})

// Error handler
app.onError((err, c) => {
  console.error('API Error:', err)
  return c.json({ 
    error: 'Internal Server Error', 
    message: c.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  }, 500)
})

export default app
