import { Hono } from 'hono'

type Bindings = {
  DB: D1Database
  NODE_ENV: string
}

const app = new Hono<{ Bindings: Bindings }>()

// GET /api/walkie-talkies - L<PERSON>y danh sách tất cả bộ đàm
app.get('/', async (c) => {
  try {
    const { results } = await c.env.DB.prepare(`
      SELECT * FROM walkie_talkies 
      ORDER BY created_at DESC
    `).all()

    return c.json({
      success: true,
      data: results,
      total: results.length
    })
  } catch (error) {
    console.error('Error fetching walkie-talkies:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy danh sách bộ đàm'
    }, 500)
  }
})

// GET /api/walkie-talkies/:id - Lấy thông tin chi tiết một bộ đàm
app.get('/:id', async (c) => {
  try {
    const id = c.req.param('id')
    const result = await c.env.DB.prepare(`
      SELECT * FROM walkie_talkies WHERE id = ?
    `).bind(id).first()

    if (!result) {
      return c.json({
        success: false,
        error: 'Không tìm thấy bộ đàm'
      }, 404)
    }

    return c.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Error fetching walkie-talkie:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy thông tin bộ đàm'
    }, 500)
  }
})

// POST /api/walkie-talkies - Tạo bộ đàm mới
app.post('/', async (c) => {
  try {
    const body = await c.req.json()
    const { model, serial_number, frequency, status, location, assigned_to } = body

    // Validation
    if (!model || !serial_number) {
      return c.json({
        success: false,
        error: 'Model và số serial là bắt buộc'
      }, 400)
    }

    const result = await c.env.DB.prepare(`
      INSERT INTO walkie_talkies (model, serial_number, frequency, status, location, assigned_to, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
    `).bind(model, serial_number, frequency || null, status || 'available', location || null, assigned_to || null).run()

    return c.json({
      success: true,
      data: { id: result.meta.last_row_id, ...body },
      message: 'Tạo bộ đàm thành công'
    }, 201)
  } catch (error) {
    console.error('Error creating walkie-talkie:', error)
    return c.json({
      success: false,
      error: 'Không thể tạo bộ đàm mới'
    }, 500)
  }
})

// PUT /api/walkie-talkies/:id - Cập nhật thông tin bộ đàm
app.put('/:id', async (c) => {
  try {
    const id = c.req.param('id')
    const body = await c.req.json()
    const { model, serial_number, frequency, status, location, assigned_to } = body

    const result = await c.env.DB.prepare(`
      UPDATE walkie_talkies 
      SET model = ?, serial_number = ?, frequency = ?, status = ?, location = ?, assigned_to = ?, updated_at = datetime('now')
      WHERE id = ?
    `).bind(model, serial_number, frequency, status, location, assigned_to, id).run()

    if (result.changes === 0) {
      return c.json({
        success: false,
        error: 'Không tìm thấy bộ đàm để cập nhật'
      }, 404)
    }

    return c.json({
      success: true,
      message: 'Cập nhật bộ đàm thành công'
    })
  } catch (error) {
    console.error('Error updating walkie-talkie:', error)
    return c.json({
      success: false,
      error: 'Không thể cập nhật bộ đàm'
    }, 500)
  }
})

// DELETE /api/walkie-talkies/:id - Xóa bộ đàm
app.delete('/:id', async (c) => {
  try {
    const id = c.req.param('id')
    
    const result = await c.env.DB.prepare(`
      DELETE FROM walkie_talkies WHERE id = ?
    `).bind(id).run()

    if (result.changes === 0) {
      return c.json({
        success: false,
        error: 'Không tìm thấy bộ đàm để xóa'
      }, 404)
    }

    return c.json({
      success: true,
      message: 'Xóa bộ đàm thành công'
    })
  } catch (error) {
    console.error('Error deleting walkie-talkie:', error)
    return c.json({
      success: false,
      error: 'Không thể xóa bộ đàm'
    }, 500)
  }
})

export default app
