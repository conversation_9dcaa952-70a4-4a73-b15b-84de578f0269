import { Hono } from 'hono'

type Bindings = {
  DB: D1Database
  NODE_ENV: string
}

const app = new Hono<{ Bindings: Bindings }>()

// GET /api/history - L<PERSON><PERSON> lịch sử hoạt động
app.get('/', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '50')
    const offset = (page - 1) * limit
    
    const type = c.req.query('type') // 'walkie_talkie' hoặc 'access_card'
    const action = c.req.query('action') // 'create', 'update', 'delete', 'assign', 'return'

    let query = `
      SELECT * FROM activity_history 
      WHERE 1=1
    `
    const params: any[] = []

    if (type) {
      query += ` AND item_type = ?`
      params.push(type)
    }

    if (action) {
      query += ` AND action = ?`
      params.push(action)
    }

    query += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`
    params.push(limit, offset)

    const { results } = await c.env.DB.prepare(query).bind(...params).all()

    // Đếm tổng số records
    let countQuery = `SELECT COUNT(*) as total FROM activity_history WHERE 1=1`
    const countParams: any[] = []

    if (type) {
      countQuery += ` AND item_type = ?`
      countParams.push(type)
    }

    if (action) {
      countQuery += ` AND action = ?`
      countParams.push(action)
    }

    const countResult = await c.env.DB.prepare(countQuery).bind(...countParams).first()
    const total = countResult?.total || 0

    return c.json({
      success: true,
      data: results,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching history:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy lịch sử hoạt động'
    }, 500)
  }
})

// GET /api/history/:id - Lấy chi tiết một bản ghi lịch sử
app.get('/:id', async (c) => {
  try {
    const id = c.req.param('id')
    const result = await c.env.DB.prepare(`
      SELECT * FROM activity_history WHERE id = ?
    `).bind(id).first()

    if (!result) {
      return c.json({
        success: false,
        error: 'Không tìm thấy bản ghi lịch sử'
      }, 404)
    }

    return c.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Error fetching history record:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy thông tin lịch sử'
    }, 500)
  }
})

// POST /api/history - Tạo bản ghi lịch sử mới
app.post('/', async (c) => {
  try {
    const body = await c.req.json()
    const { item_type, item_id, action, description, user_id, old_values, new_values } = body

    // Validation
    if (!item_type || !item_id || !action) {
      return c.json({
        success: false,
        error: 'Loại thiết bị, ID thiết bị và hành động là bắt buộc'
      }, 400)
    }

    const result = await c.env.DB.prepare(`
      INSERT INTO activity_history (item_type, item_id, action, description, user_id, old_values, new_values, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))
    `).bind(
      item_type, 
      item_id, 
      action, 
      description || null, 
      user_id || null,
      old_values ? JSON.stringify(old_values) : null,
      new_values ? JSON.stringify(new_values) : null
    ).run()

    return c.json({
      success: true,
      data: { id: result.meta.last_row_id, ...body },
      message: 'Tạo bản ghi lịch sử thành công'
    }, 201)
  } catch (error) {
    console.error('Error creating history record:', error)
    return c.json({
      success: false,
      error: 'Không thể tạo bản ghi lịch sử'
    }, 500)
  }
})

// GET /api/history/item/:type/:id - Lấy lịch sử của một thiết bị cụ thể
app.get('/item/:type/:id', async (c) => {
  try {
    const itemType = c.req.param('type')
    const itemId = c.req.param('id')

    const { results } = await c.env.DB.prepare(`
      SELECT * FROM activity_history 
      WHERE item_type = ? AND item_id = ?
      ORDER BY created_at DESC
    `).bind(itemType, itemId).all()

    return c.json({
      success: true,
      data: results,
      total: results.length
    })
  } catch (error) {
    console.error('Error fetching item history:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy lịch sử thiết bị'
    }, 500)
  }
})

// GET /api/history/user/:userId - Lấy lịch sử hoạt động của một người dùng
app.get('/user/:userId', async (c) => {
  try {
    const userId = c.req.param('userId')
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '20')
    const offset = (page - 1) * limit

    const { results } = await c.env.DB.prepare(`
      SELECT * FROM activity_history 
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `).bind(userId, limit, offset).all()

    const countResult = await c.env.DB.prepare(`
      SELECT COUNT(*) as total FROM activity_history WHERE user_id = ?
    `).bind(userId).first()
    const total = countResult?.total || 0

    return c.json({
      success: true,
      data: results,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching user history:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy lịch sử người dùng'
    }, 500)
  }
})

export default app
