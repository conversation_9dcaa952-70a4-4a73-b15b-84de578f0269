-- Database schema cho hệ thống quản lý thiết bị

-- <PERSON><PERSON><PERSON> bộ đàm (Walkie Talkies)
CREATE TABLE IF NOT EXISTS walkie_talkies (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  model TEXT NOT NULL,
  serial_number TEXT UNIQUE NOT NULL,
  frequency TEXT,
  status TEXT DEFAULT 'available' CHECK (status IN ('available', 'assigned', 'maintenance', 'lost')),
  location TEXT,
  assigned_to TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON> thẻ từ (Access Cards)
CREATE TABLE IF NOT EXISTS access_cards (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  card_number TEXT UNIQUE NOT NULL,
  card_type TEXT NOT NULL CHECK (card_type IN ('employee', 'visitor', 'contractor', 'admin')),
  access_level TEXT DEFAULT 'basic' CHECK (access_level IN ('basic', 'medium', 'high', 'admin')),
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'expired', 'lost')),
  assigned_to TEXT,
  expiry_date DATE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Bảng lịch sử hoạt động
CREATE TABLE IF NOT EXISTS activity_history (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  item_type TEXT NOT NULL CHECK (item_type IN ('walkie_talkie', 'access_card')),
  item_id INTEGER NOT NULL,
  action TEXT NOT NULL CHECK (action IN ('create', 'update', 'delete', 'assign', 'return', 'maintenance')),
  description TEXT,
  user_id TEXT,
  old_values TEXT, -- JSON string
  new_values TEXT, -- JSON string
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Indexes để tối ưu hiệu suất
CREATE INDEX IF NOT EXISTS idx_walkie_talkies_status ON walkie_talkies(status);
CREATE INDEX IF NOT EXISTS idx_walkie_talkies_assigned_to ON walkie_talkies(assigned_to);
CREATE INDEX IF NOT EXISTS idx_access_cards_status ON access_cards(status);
CREATE INDEX IF NOT EXISTS idx_access_cards_assigned_to ON access_cards(assigned_to);
CREATE INDEX IF NOT EXISTS idx_activity_history_item ON activity_history(item_type, item_id);
CREATE INDEX IF NOT EXISTS idx_activity_history_user ON activity_history(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_history_created_at ON activity_history(created_at);

-- Dữ liệu mẫu cho testing
INSERT OR IGNORE INTO walkie_talkies (model, serial_number, frequency, status, location) VALUES
('Motorola GP328', 'MT001', '400.000 MHz', 'available', 'Kho thiết bị'),
('Kenwood TK-3107', 'KW002', '446.000 MHz', 'assigned', 'Phòng bảo vệ'),
('Baofeng UV-5R', 'BF003', '144.000 MHz', 'maintenance', 'Phòng kỹ thuật');

INSERT OR IGNORE INTO access_cards (card_number, card_type, access_level, status, assigned_to) VALUES
('AC001', 'employee', 'medium', 'active', 'Nguyễn Văn A'),
('AC002', 'visitor', 'basic', 'active', 'Trần Thị B'),
('AC003', 'admin', 'high', 'active', 'Lê Văn C');

INSERT OR IGNORE INTO activity_history (item_type, item_id, action, description, user_id) VALUES
('walkie_talkie', 1, 'create', 'Tạo bộ đàm mới Motorola GP328', 'admin'),
('access_card', 1, 'create', 'Tạo thẻ từ mới cho nhân viên', 'admin'),
('walkie_talkie', 2, 'assign', 'Giao bộ đàm cho phòng bảo vệ', 'admin');
