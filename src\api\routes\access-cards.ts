import { Hono } from 'hono'

type Bindings = {
  DB: D1Database
  NODE_ENV: string
}

const app = new Hono<{ Bindings: Bindings }>()

// GET /api/access-cards - L<PERSON>y danh sách tất cả thẻ từ
app.get('/', async (c) => {
  try {
    const { results } = await c.env.DB.prepare(`
      SELECT * FROM access_cards 
      ORDER BY created_at DESC
    `).all()

    return c.json({
      success: true,
      data: results,
      total: results.length
    })
  } catch (error) {
    console.error('Error fetching access-cards:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy danh sách thẻ từ'
    }, 500)
  }
})

// GET /api/access-cards/:id - L<PERSON>y thông tin chi tiết một thẻ từ
app.get('/:id', async (c) => {
  try {
    const id = c.req.param('id')
    const result = await c.env.DB.prepare(`
      SELECT * FROM access_cards WHERE id = ?
    `).bind(id).first()

    if (!result) {
      return c.json({
        success: false,
        error: '<PERSON>hông tìm thấy thẻ từ'
      }, 404)
    }

    return c.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Error fetching access-card:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy thông tin thẻ từ'
    }, 500)
  }
})

// POST /api/access-cards - Tạo thẻ từ mới
app.post('/', async (c) => {
  try {
    const body = await c.req.json()
    const { card_number, card_type, access_level, status, assigned_to, expiry_date } = body

    // Validation
    if (!card_number || !card_type) {
      return c.json({
        success: false,
        error: 'Số thẻ và loại thẻ là bắt buộc'
      }, 400)
    }

    const result = await c.env.DB.prepare(`
      INSERT INTO access_cards (card_number, card_type, access_level, status, assigned_to, expiry_date, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
    `).bind(card_number, card_type, access_level || 'basic', status || 'active', assigned_to || null, expiry_date || null).run()

    return c.json({
      success: true,
      data: { id: result.meta.last_row_id, ...body },
      message: 'Tạo thẻ từ thành công'
    }, 201)
  } catch (error) {
    console.error('Error creating access-card:', error)
    return c.json({
      success: false,
      error: 'Không thể tạo thẻ từ mới'
    }, 500)
  }
})

// PUT /api/access-cards/:id - Cập nhật thông tin thẻ từ
app.put('/:id', async (c) => {
  try {
    const id = c.req.param('id')
    const body = await c.req.json()
    const { card_number, card_type, access_level, status, assigned_to, expiry_date } = body

    const result = await c.env.DB.prepare(`
      UPDATE access_cards 
      SET card_number = ?, card_type = ?, access_level = ?, status = ?, assigned_to = ?, expiry_date = ?, updated_at = datetime('now')
      WHERE id = ?
    `).bind(card_number, card_type, access_level, status, assigned_to, expiry_date, id).run()

    if (result.changes === 0) {
      return c.json({
        success: false,
        error: 'Không tìm thấy thẻ từ để cập nhật'
      }, 404)
    }

    return c.json({
      success: true,
      message: 'Cập nhật thẻ từ thành công'
    })
  } catch (error) {
    console.error('Error updating access-card:', error)
    return c.json({
      success: false,
      error: 'Không thể cập nhật thẻ từ'
    }, 500)
  }
})

// DELETE /api/access-cards/:id - Xóa thẻ từ
app.delete('/:id', async (c) => {
  try {
    const id = c.req.param('id')
    
    const result = await c.env.DB.prepare(`
      DELETE FROM access_cards WHERE id = ?
    `).bind(id).run()

    if (result.changes === 0) {
      return c.json({
        success: false,
        error: 'Không tìm thấy thẻ từ để xóa'
      }, 404)
    }

    return c.json({
      success: true,
      message: 'Xóa thẻ từ thành công'
    })
  } catch (error) {
    console.error('Error deleting access-card:', error)
    return c.json({
      success: false,
      error: 'Không thể xóa thẻ từ'
    }, 500)
  }
})

// GET /api/access-cards/by-user/:userId - Lấy thẻ từ theo người dùng
app.get('/by-user/:userId', async (c) => {
  try {
    const userId = c.req.param('userId')
    const { results } = await c.env.DB.prepare(`
      SELECT * FROM access_cards WHERE assigned_to = ?
      ORDER BY created_at DESC
    `).bind(userId).all()

    return c.json({
      success: true,
      data: results,
      total: results.length
    })
  } catch (error) {
    console.error('Error fetching access-cards by user:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy danh sách thẻ từ của người dùng'
    }, 500)
  }
})

export default app
